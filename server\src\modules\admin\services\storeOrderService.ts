import prisma from '../../../config/prismaClient';

export const getAllStoreOrders = async (filters?: {
  status?: string;
  search?: string;
  startDate?: string;
  endDate?: string;
}) => {
  try {
    console.log('=== STORE ORDERS SERVICE CALLED ===');
    console.log('Filters received:', filters);

    // SIMPLE APPROACH: Just get orders without any relationships first
    console.log('Fetching orders without relationships...');

    const where: any = {};

    if (filters?.status) {
      where.status = filters.status;
    }

    if (filters?.startDate || filters?.endDate) {
      where.createdAt = {};
      if (filters.startDate) {
        where.createdAt.gte = new Date(filters.startDate);
      }
      if (filters.endDate) {
        where.createdAt.lte = new Date(filters.endDate);
      }
    }

    // Add search filter
    if (filters?.search) {
      const searchTerm = filters.search.toLowerCase();
      where.OR = [
        { id: { contains: searchTerm, mode: 'insensitive' } },
        { studentId: { contains: searchTerm, mode: 'insensitive' } },
        { studentName: { contains: searchTerm, mode: 'insensitive' } },
        { studentEmail: { contains: searchTerm, mode: 'insensitive' } },
        { itemName: { contains: searchTerm, mode: 'insensitive' } }
      ];
    }

    console.log('Where clause:', JSON.stringify(where, null, 2));

    // Get orders without any relationships to avoid foreign key issues
    const orders = await prisma.storeOrder.findMany({
      where,
      orderBy: { createdAt: 'desc' }
    });

    console.log(`Successfully fetched ${orders.length} orders`);

    // Log first few orders for debugging
    if (orders.length > 0) {
      console.log('Sample order:', orders[0]);
    }

    return orders;

  } catch (error: any) {
    console.error('=== STORE ORDERS SERVICE ERROR ===');
    console.error('Error message:', error.message);
    console.error('Error code:', error.code);
    console.error('Full error:', error);

    // Return empty array instead of throwing error
    console.log('Returning empty array due to error');
    return [];
  }
};

export const getStoreOrderById = async (orderId: string) => {
  try {
    const order = await prisma.storeOrder.findUnique({
      where: { id: orderId }
    });

    if (!order) {
      throw new Error('Order not found');
    }

    return order;
  } catch (error: any) {
    throw new Error('Failed to fetch order details');
  }
};

export const getStoreOrderStats = async () => {
  try {
    const [
      totalOrders,
      completedOrders,
      pendingOrders,
      cancelledOrders,
      totalRevenue,
      todayOrders,
      thisMonthOrders
    ] = await Promise.all([
      prisma.storeOrder.count(),
      prisma.storeOrder.count({ where: { status: 'COMPLETED' } }),
      prisma.storeOrder.count({ where: { status: 'PENDING' } }),
      prisma.storeOrder.count({ where: { status: 'CANCELLED' } }),
      prisma.storeOrder.aggregate({
        where: { status: 'COMPLETED' },
        _sum: { totalCoins: true }
      }),
      prisma.storeOrder.count({
        where: {
          createdAt: {
            gte: new Date(new Date().setHours(0, 0, 0, 0))
          }
        }
      }),
      prisma.storeOrder.count({
        where: {
          createdAt: {
            gte: new Date(new Date().getFullYear(), new Date().getMonth(), 1)
          }
        }
      })
    ]);

    return {
      totalOrders,
      completedOrders,
      pendingOrders,
      cancelledOrders,
      totalRevenue: totalRevenue._sum.totalCoins || 0,
      todayOrders,
      thisMonthOrders
    };
  } catch (error: any) {
    throw new Error('Failed to fetch order statistics');
  }
};

export const updateOrderStatus = async (orderId: string, status: string) => {
  try {
    const validStatuses = ['PENDING', 'COMPLETED', 'CANCELLED'];
    if (!validStatuses.includes(status)) {
      throw new Error('Invalid order status');
    }

    const order = await prisma.storeOrder.update({
      where: { id: orderId },
      data: { status: status as any }
    });

    return order;
  } catch (error: any) {
    throw new Error('Failed to update order status');
  }
};

export const createTestStoreOrder = async (data: {
  studentName: string;
  studentEmail?: string;
  itemName: string;
  itemPrice: number;
  quantity: number;
  totalCoins: number;
}) => {
  try {
    console.log('=== CREATING TEST STORE ORDER ===');
    console.log('Test order data:', data);

    const testOrder = await prisma.storeOrder.create({
      data: {
        studentId: `test-student-${Date.now()}`,
        studentName: data.studentName,
        studentEmail: data.studentEmail || null,
        itemId: `test-item-${Date.now()}`,
        itemName: data.itemName,
        itemPrice: data.itemPrice,
        quantity: data.quantity,
        totalCoins: data.totalCoins,
        status: 'COMPLETED'
      }
    });

    console.log('Test order created successfully:', testOrder);
    return testOrder;
  } catch (error: any) {
    console.error('Error creating test order:', error);
    throw new Error('Failed to create test order: ' + error.message);
  }
};
