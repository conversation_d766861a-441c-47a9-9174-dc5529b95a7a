import { Request, Response } from 'express';
import { getAllStoreOrders, getStoreOrderById, getStoreOrderStats, updateOrderStatus, createTestStoreOrder } from '../services/storeOrderService';
import { sendSuccess, sendError } from '@/utils/response';

export const getAllOrders = async (req: Request, res: Response): Promise<void> => {
  try {
    console.log('=== GET ALL ORDERS API CALLED ===');
    console.log('Query params:', req.query);

    const { status, search, startDate, endDate } = req.query;

    const orders = await getAllStoreOrders({
      status: status as string,
      search: search as string,
      startDate: startDate as string,
      endDate: endDate as string
    });

    console.log(`Successfully retrieved ${orders.length} orders`);
    sendSuccess(res, orders, 'Store orders retrieved successfully');
  } catch (error: any) {
    console.error('=== GET ALL ORDERS ERROR ===');
    console.error('Error details:', error);
    sendError(res, error.message || 'Failed to retrieve store orders', 500);
  }
};

export const getOrderDetails = async (req: Request, res: Response): Promise<void> => {
  try {
    const { orderId } = req.params;
    if (!orderId) {
      sendError(res, 'Order ID is required', 400);
      return;
    }

    const order = await getStoreOrderById(orderId);
    sendSuccess(res, order, 'Order details retrieved successfully');
  } catch (error: any) {
    sendError(res, error.message || 'Failed to retrieve order details', 500);
  }
};

export const getOrderStats = async (req: Request, res: Response): Promise<void> => {
  try {
    const stats = await getStoreOrderStats();
    sendSuccess(res, stats, 'Order statistics retrieved successfully');
  } catch (error: any) {
    sendError(res, error.message || 'Failed to retrieve order statistics', 500);
  }
};

export const updateOrder = async (req: Request, res: Response): Promise<void> => {
  try {
    const { orderId } = req.params;
    const { status } = req.body;

    if (!orderId) {
      sendError(res, 'Order ID is required', 400);
      return;
    }

    if (!status) {
      sendError(res, 'Status is required', 400);
      return;
    }

    const order = await updateOrderStatus(orderId, status);
    sendSuccess(res, order, 'Order status updated successfully');
  } catch (error: any) {
    sendError(res, error.message || 'Failed to update order status', 500);
  }
};

export const createTestOrder = async (req: Request, res: Response): Promise<void> => {
  try {
    console.log('=== CREATE TEST ORDER API CALLED ===');
    console.log('Request body:', req.body);

    const { studentName, studentEmail, itemName, itemPrice, quantity, totalCoins } = req.body;

    if (!studentName || !itemName || !itemPrice || !quantity || !totalCoins) {
      sendError(res, 'Missing required fields', 400);
      return;
    }

    const testOrder = await createTestStoreOrder({
      studentName,
      studentEmail,
      itemName,
      itemPrice: parseInt(itemPrice),
      quantity: parseInt(quantity),
      totalCoins: parseInt(totalCoins)
    });

    console.log('Test order created successfully:', testOrder);
    sendSuccess(res, testOrder, 'Test order created successfully', 201);
  } catch (error: any) {
    console.error('=== CREATE TEST ORDER ERROR ===');
    console.error('Error details:', error);
    sendError(res, error.message || 'Failed to create test order', 500);
  }
};
