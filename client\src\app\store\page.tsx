"use client";

import { useState, useEffect } from "react";
import Image from "next/image";
import { ShoppingBag, Star, Filter, Search, Coins, ShoppingCart, Plus, Minus } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardFooter } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import { toast } from "sonner";
import Header from "@/app-components/Header";
import Footer from "@/app-components/Footer";
import { isStudentAuthenticated, getStudentId } from "@/lib/utils";
import * as storeApi from "@/services/storeApi";
import * as storePurchaseApi from "@/services/storePurchaseApi";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogDes<PERSON>,
  DialogFooter,
  Di<PERSON>Header,
  Dialog<PERSON>itle,
} from "@/components/ui/dialog";

const getImageUrl = (imagePath: string | null): string => {
  if (!imagePath) return '/logo.png';

  if (imagePath.startsWith('http')) return imagePath;

  if (imagePath.startsWith('/uploads')) {
    const baseURL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:4005/api/v1';
    const serverURL = baseURL.replace('/api/v1', '');
    return `${serverURL}${imagePath}`;
  }

  if (imagePath && !imagePath.includes('/')) {
    const baseURL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:4005/api/v1';
    const serverURL = baseURL.replace('/api/v1', '');
    return `${serverURL}/uploads/store/${imagePath}`;
  }

  return '/logo.png';
};

const categories = ["All", "Stationery", "Electronics", "Apparel", "Accessories", "Digital", "Books", "Sports", "Other"];

interface CartItem {
  id: string;
  name: string;
  coinPrice: number;
  quantity: number;
  image: string;
}

const StorePage = () => {
  const [products, setProducts] = useState<storeApi.StoreItem[]>([]);
  const [filteredProducts, setFilteredProducts] = useState<storeApi.StoreItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedCategory, setSelectedCategory] = useState("All");
  const [sortBy, setSortBy] = useState("name");
  const [cart, setCart] = useState<CartItem[]>([]);
  const [showCart, setShowCart] = useState(false);
  const [paymentMethod] = useState<"coins">("coins");
  const [isStudentLoggedIn, setIsStudentLoggedIn] = useState(false);

  useEffect(() => {
    setIsStudentLoggedIn(isStudentAuthenticated());
    loadStoreItems();
  }, []);

  const loadStoreItems = async () => {
    try {
      setLoading(true);
      const items = await storeApi.getAllStoreItems();

      console.log('=== CLIENT STORE ITEMS LOADED ===');
      console.log(`Loaded ${items.length} items from server:`);
      items.forEach(item => {
        console.log(`- ID: ${item.id}, Name: ${item.name}, Status: ${item.status}`);
      });

      setProducts(items);
    } catch (error: any) {
      console.error('Failed to load store items:', error);
      toast.error(error.message || 'Failed to load store items');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    let filtered = products;

    if (selectedCategory !== "All") {
      filtered = filtered.filter(product => product.category === selectedCategory);
    }

    if (searchQuery) {
      filtered = filtered.filter(product =>
        product.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        product.description.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    switch (sortBy) {
      case "coin-price-low":
        filtered = [...filtered].sort((a, b) => a.coinPrice - b.coinPrice);
        break;
      case "coin-price-high":
        filtered = [...filtered].sort((a, b) => b.coinPrice - a.coinPrice);
        break;
      case "name":
        filtered = [...filtered].sort((a, b) => a.name.localeCompare(b.name));
        break;
      case "newest":
        filtered = [...filtered].sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
        break;
      default:
        filtered = [...filtered].sort((a, b) => a.name.localeCompare(b.name));
        break;
    }

    setFilteredProducts(filtered);
  }, [products, selectedCategory, searchQuery, sortBy]);

  const addToCart = (product: storeApi.StoreItem) => {
    if (!isStudentLoggedIn) {
      toast.error("Please login to add items to cart");
      return;
    }

    if (product.quantity === 0) {
      toast.error("Item is out of stock");
      return;
    }

    const existingItem = cart.find(item => item.id === product.id);
    if (existingItem) {
      setCart(cart.map(item =>
        item.id === product.id
          ? { ...item, quantity: item.quantity + 1 }
          : item
      ));
    } else {
      setCart([...cart, {
        id: product.id,
        name: product.name,
        coinPrice: product.coinPrice,
        quantity: 1,
        image: getImageUrl(product.image)
      }]);
    }
    toast.success("Item added to cart!");
  };

  const removeFromCart = (productId: string) => {
    setCart(cart.filter(item => item.id !== productId));
    toast.success("Item removed from cart!");
  };

  const updateQuantity = (productId: string, newQuantity: number) => {
    if (newQuantity === 0) {
      removeFromCart(productId);
      return;
    }
    setCart(cart.map(item =>
      item.id === productId
        ? { ...item, quantity: newQuantity }
        : item
    ));
  };

  const getTotalPrice = () => {
    return cart.reduce((total, item) => {
      return total + (item.coinPrice * item.quantity);
    }, 0);
  };

  const handleCheckout = async () => {
    if (cart.length === 0) {
      toast.error("Your cart is empty!");
      return;
    }

    if (!isStudentLoggedIn) {
      toast.error("Please login to complete purchase");
      return;
    }

    try {
      const totalCoins = getTotalPrice();
      const studentId = getStudentId();

      console.log('=== CLIENT PURCHASE DEBUG ===');
      console.log('Student ID:', studentId);
      console.log('Is student authenticated:', isStudentLoggedIn);
      console.log('Cart items being purchased:', cart);
      console.log('Total coins:', totalCoins);

      const purchaseData: storePurchaseApi.PurchaseData = {
        cartItems: cart.map(item => ({
          id: item.id,
          name: item.name,
          coinPrice: item.coinPrice,
          quantity: item.quantity,
          image: item.image
        })),
        totalCoins
      };

      console.log('Purchase data being sent:', purchaseData);
      console.log('Item IDs in cart:', cart.map(item => `${item.name}: ${item.id}`));

      const result = await storePurchaseApi.purchaseItems(purchaseData);

      console.log('Purchase result:', result);
      toast.success(`Purchase completed successfully! Order ID: ${result.orderId.slice(-8)}`);

      // Clear cart and close modal
      setCart([]);
      setShowCart(false);

      // Reload store items to update stock
      loadStoreItems();

      console.log('Purchase completed successfully. Order should now appear in admin panel.');
    } catch (error: any) {
      console.error('=== CLIENT PURCHASE ERROR ===');
      console.error('Error details:', error);
      toast.error(error.message || 'Purchase failed');
    }
  };

  return (
    <>
      <Header />
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
        {/* Hero Section */}
        <div className="relative bg-background dark:bg-gray-900 py-20 overflow-hidden border-b">
          <div className="container mx-auto px-4 text-center relative z-10">
            <div className="flex items-center justify-center gap-4 mb-6">
              <div className="p-3 bg-customOrange/10 rounded-xl">
                <ShoppingBag className="w-12 h-12 text-customOrange" />
              </div>
              <h1 className="text-5xl md:text-6xl font-bold text-foreground">
                UEST Store
              </h1>
            </div>
            <p className="text-xl md:text-2xl mb-8 text-muted-foreground max-w-3xl mx-auto">
              Premium educational products for students
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
              <div className="flex items-center gap-2 px-4 py-2 bg-card rounded-full shadow-sm border">
                <Coins className="w-5 h-5 text-customOrange" />
                <span className="text-sm font-medium text-card-foreground">Pay with UEST Coins</span>
              </div>
              <div className="flex items-center gap-2 px-4 py-2 bg-card rounded-full shadow-sm border">
                <span className="w-2 h-2 bg-customOrange rounded-full"></span>
                <span className="text-sm font-medium text-card-foreground">Quality Products</span>
              </div>
            </div>
          </div>
        </div>

        {/* Filters and Search */}
        <div className="container mx-auto px-4 py-8">
          <div className="bg-card rounded-xl shadow-sm border p-6 mb-8">
            <div className="flex flex-col lg:flex-row gap-4">
              {/* Search */}
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
                <Input
                  placeholder="Search products..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10"
                />
              </div>

              {/* Filters */}
              <div className="flex flex-col sm:flex-row gap-4">
                {/* Category Filter */}
                <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                  <SelectTrigger className="w-full sm:w-48">
                    <Filter className="w-4 h-4 mr-2 text-customOrange" />
                    <SelectValue placeholder="Category" />
                  </SelectTrigger>
                  <SelectContent>
                    {categories.map((category) => (
                      <SelectItem key={category} value={category}>
                        {category}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>

                {/* Sort */}
                <Select value={sortBy} onValueChange={setSortBy}>
                  <SelectTrigger className="w-full sm:w-48">
                    <SelectValue placeholder="Sort by" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="name">Name (A-Z)</SelectItem>
                    <SelectItem value="coin-price-low">Coins: Low to High</SelectItem>
                    <SelectItem value="coin-price-high">Coins: High to Low</SelectItem>
                    <SelectItem value="newest">Newest First</SelectItem>
                  </SelectContent>
                </Select>

                {/* Cart Button */}
                <Button
                  onClick={() => setShowCart(true)}
                  className="bg-customOrange hover:bg-orange-600 relative"
                >
                  <ShoppingCart className="w-4 h-4 mr-2" />
                  Cart
                  {cart.length > 0 && (
                    <Badge className="absolute -top-2 -right-2 bg-destructive text-destructive-foreground rounded-full w-5 h-5 flex items-center justify-center text-xs">
                      {cart.reduce((total, item) => total + item.quantity, 0)}
                    </Badge>
                  )}
                </Button>
              </div>
            </div>
          </div>

          {/* Payment Method Info */}
          <div className="flex flex-col sm:flex-row items-center justify-between gap-4 mb-8 p-6 bg-card rounded-xl border">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-customOrange/10 rounded-lg">
                <Coins className="w-5 h-5 text-customOrange" />
              </div>
              <div>
                <h3 className="font-semibold text-card-foreground">Payment Method</h3>
                <p className="text-sm text-muted-foreground">All items are priced in UEST Coins</p>
              </div>
            </div>
            <div className="flex items-center gap-2 px-4 py-2 bg-orange-50 border border-orange-200 rounded-lg">
              <Coins className="w-4 h-4 text-orange-600" />
              <span className="font-medium text-orange-800">UEST Coins Only</span>
            </div>
          </div>

          {/* Products Grid */}
          {loading ? (
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
              {[...Array(8)].map((_, index) => (
                <Card key={index} className="overflow-hidden">
                  <Skeleton className="h-48 w-full" />
                  <CardContent className="p-4">
                    <Skeleton className="h-4 w-3/4 mb-2" />
                    <Skeleton className="h-3 w-full mb-2" />
                    <Skeleton className="h-3 w-2/3" />
                  </CardContent>
                  <CardFooter className="p-4">
                    <Skeleton className="h-10 w-full" />
                  </CardFooter>
                </Card>
              ))}
            </div>
          ) : (
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
              {filteredProducts.map((product, index) => (
                <Card
                  key={product.id}
                  className="overflow-hidden store-card-hover group bg-card border hover:shadow-lg transition-all duration-300 animate-fade-in-up"
                  style={{ animationDelay: `${index * 0.1}s` }}
                >
                  <div className="relative h-64 bg-muted/30">
                    <Image
                      src={getImageUrl(product.image)}
                      alt={product.name}
                      fill
                      className="object-cover group-hover:scale-105 transition-transform duration-300"
                      onError={(e) => {
                        const target = e.target as HTMLImageElement;
                        target.src = "/logo.png";
                      }}
                    />
                    {product.quantity === 0 && (
                      <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center">
                        <Badge variant="destructive">Out of Stock</Badge>
                      </div>
                    )}
                  </div>

                  <CardContent className="p-4">
                    <h3 className="font-semibold text-lg mb-2 text-card-foreground line-clamp-1">{product.name}</h3>
                    <p className="text-muted-foreground text-sm mb-3 line-clamp-2">
                      {product.description}
                    </p>

                    <div className="space-y-3">
                      <div className="flex items-center justify-between">
                        <span className="text-2xl font-bold text-customOrange flex items-center">
                          <Coins className="w-5 h-5 mr-1" />
                          {product.coinPrice} coins
                        </span>
                        <Badge variant="secondary" className="text-xs">
                          {product.category}
                        </Badge>
                      </div>
                      <div className="text-sm text-muted-foreground">
                        Stock: {product.quantity} available
                      </div>
                    </div>
                  </CardContent>

                  <CardFooter className="p-4 pt-0">
                    <Button
                      onClick={() => addToCart(product)}
                      disabled={product.quantity === 0}
                      className="w-full bg-customOrange hover:bg-orange-600 disabled:opacity-50"
                    >
                      {product.quantity > 0 ? (
                        <>
                          <ShoppingCart className="w-4 h-4 mr-2" />
                          Add to Cart
                        </>
                      ) : (
                        "Out of Stock"
                      )}
                    </Button>
                  </CardFooter>
                </Card>
              ))}
            </div>
          )}

          {filteredProducts.length === 0 && !loading && (
            <div className="text-center py-16">
              <ShoppingBag className="w-16 h-16 mx-auto text-muted-foreground mb-4" />
              <h3 className="text-xl font-semibold text-card-foreground mb-2">
                No products found
              </h3>
              <p className="text-muted-foreground">
                Try adjusting your search or filter criteria
              </p>
            </div>
          )}
        </div>

        {/* Shopping Cart Dialog */}
        <Dialog open={showCart} onOpenChange={setShowCart}>
          <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle className="flex items-center gap-2">
                <ShoppingCart className="w-5 h-5" />
                Shopping Cart ({cart.reduce((total, item) => total + item.quantity, 0)} items)
              </DialogTitle>
              <DialogDescription>
                Review your items before checkout
              </DialogDescription>
            </DialogHeader>

            <div className="space-y-4">
              {cart.length === 0 ? (
                <div className="text-center py-8">
                  <ShoppingCart className="w-12 h-12 mx-auto text-muted-foreground mb-4" />
                  <p className="text-muted-foreground">Your cart is empty</p>
                </div>
              ) : (
                <>
                  {cart.map((item) => (
                    <div key={item.id} className="flex items-center gap-4 p-4 border rounded-lg bg-card">
                      <Image
                        src={item.image}
                        alt={item.name}
                        width={60}
                        height={60}
                        className="rounded object-cover"
                        onError={(e) => {
                          const target = e.target as HTMLImageElement;
                          target.src = "/placeholder-product.jpg";
                        }}
                      />
                      <div className="flex-1">
                        <h4 className="font-medium text-card-foreground">{item.name}</h4>
                        <p className="text-customOrange font-semibold flex items-center">
                          <Coins className="w-4 h-4 mr-1" />
                          {item.coinPrice} coins
                        </p>
                      </div>
                      <div className="flex items-center gap-2">
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => updateQuantity(item.id, item.quantity - 1)}
                        >
                          <Minus className="w-3 h-3" />
                        </Button>
                        <span className="w-8 text-center">{item.quantity}</span>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => updateQuantity(item.id, item.quantity + 1)}
                        >
                          <Plus className="w-3 h-3" />
                        </Button>
                      </div>
                      <Button
                        size="sm"
                        variant="destructive"
                        onClick={() => removeFromCart(item.id)}
                      >
                        Remove
                      </Button>
                    </div>
                  ))}

                  <div className="border-t pt-4">
                    <div className="flex justify-between items-center text-lg font-semibold">
                      <span>Total:</span>
                      <span className="text-customOrange flex items-center">
                        <Coins className="w-5 h-5 mr-1" />
                        {getTotalPrice()} coins
                      </span>
                    </div>
                  </div>
                </>
              )}
            </div>

            <DialogFooter>
              <Button variant="outline" onClick={() => setShowCart(false)}>
                Continue Shopping
              </Button>
              {cart.length > 0 && (
                <Button onClick={handleCheckout} className="bg-customOrange hover:bg-orange-600">
                  Checkout
                </Button>
              )}
            </DialogFooter>
          </DialogContent>
        </Dialog>

        {/* Store Info Section */}
        <div className="bg-muted/30 py-16 mt-16">
          <div className="container mx-auto px-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8 text-center">
              <div className="space-y-3">
                <div className="w-12 h-12 bg-customOrange/10 rounded-full flex items-center justify-center mx-auto">
                  <ShoppingBag className="w-6 h-6 text-customOrange" />
                </div>
                <h3 className="font-semibold text-lg text-card-foreground">Quality Products</h3>
                <p className="text-muted-foreground">Premium educational materials carefully selected for students</p>
              </div>
              <div className="space-y-3">
                <div className="w-12 h-12 bg-customOrange/10 rounded-full flex items-center justify-center mx-auto">
                  <Coins className="w-6 h-6 text-customOrange" />
                </div>
                <h3 className="font-semibold text-lg text-card-foreground">UEST Coins</h3>
                <p className="text-muted-foreground">Pay with your earned UEST coins for exclusive discounts</p>
              </div>
              <div className="space-y-3">
                <div className="w-12 h-12 bg-customOrange/10 rounded-full flex items-center justify-center mx-auto">
                  <Star className="w-6 h-6 text-customOrange" />
                </div>
                <h3 className="font-semibold text-lg text-card-foreground">Student Focused</h3>
                <p className="text-muted-foreground">Everything designed with student success in mind</p>
              </div>
            </div>
          </div>
        </div>
      </div>
      <Footer />
    </>
  );
};

export default StorePage;