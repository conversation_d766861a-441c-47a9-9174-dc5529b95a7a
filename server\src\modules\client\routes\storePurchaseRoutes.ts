import { Router } from 'express';
import { purchaseItems, getMyOrders, getOrderDetails } from '../controllers/storePurchaseController';
import { studentAuthMiddleware } from '@/middlewares/studentAuth';

const router = Router();

// Purchase items from store
router.post('/purchase', studentAuthMiddleware, purchaseItems);

// Get student's orders
router.get('/orders', studentAuthMiddleware, getMyOrders);

// Get specific order details
router.get('/orders/:orderId', studentAuthMiddleware, getOrderDetails);

export default router;
